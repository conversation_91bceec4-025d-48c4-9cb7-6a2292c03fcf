<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.rocksdb</groupId>
    <artifactId>rocksdbjni-jmh</artifactId>
    <version>1.0-SNAPSHOT</version>

    <url>http://rocksdb.org/</url>

    <name>rocksdbjni-jmh</name>
    <description>JMH Benchmarks for RocksDB Java API</description>

    <organization>
        <name>Facebook, Inc.</name>
        <url>https://www.facebook.com</url>
    </organization>

    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GNU General Public License, version 2</name>
            <url>http://www.gnu.org/licenses/gpl-2.0.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:git://github.com/facebook/rocksdb.git</connection>
        <developerConnection>scm:git:**************:facebook/rocksdb.git</developerConnection>
        <url>http://github.com/facebook/rocksdb/</url>
    </scm>

    <properties>
        <project.build.source>1.7</project.build.source>
        <project.build.target>1.7</project.build.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <jmh.version>1.22</jmh.version>
        <uberjar.name>benchmarks</uberjar.name>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.rocksdb</groupId>
            <artifactId>rocksdbjni</artifactId>
            <version>6.6.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-core</artifactId>
            <version>${jmh.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-generator-annprocess</artifactId>
            <version>${jmh.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${project.build.source}</source>
                    <target>${project.build.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>3.0</version>
                <inherited>true</inherited>
                <configuration>
                    <header>LICENSE-HEADER.txt</header>
                    <failIfMissing>true</failIfMissing>
                    <aggregate>true</aggregate>
                    <strictCheck>true</strictCheck>
                    <excludes>
                        <exclude>pom.xml</exclude>
                    </excludes>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <finalName>${project.artifactId}-${project.version}-${uberjar.name}</finalName>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>org.openjdk.jmh.Main</mainClass>
                                </transformer>
                            </transformers>
                            <filters>
                                <filter>
                                    <!--
                                        Shading signed JARs will fail without this.
                                        http://stackoverflow.com/questions/999489/invalid-signature-file-when-attempting-to-run-a-jar
                                    -->
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>