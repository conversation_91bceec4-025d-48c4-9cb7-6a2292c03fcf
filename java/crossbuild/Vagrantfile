# -*- mode: ruby -*-
# vi: set ft=ruby :

# Vagrantfile API/syntax version. Don't touch unless you know what you're doing!
VAGRANTFILE_API_VERSION = "2"

Vagrant.configure(VAGRANTFILE_API_VERSION) do |config|

  config.vm.define "linux32" do |linux32|
    linux32.vm.box = "bento/centos-6.10-i386"
    linux32.vm.provision :shell, path: "build-linux-centos.sh"
  end

  config.vm.define "linux64" do |linux64|
    linux64.vm.box = "bento/centos-6.10"
    linux64.vm.provision :shell, path: "build-linux-centos.sh"
  end

  config.vm.define "linux32-musl" do |musl32|
    musl32.vm.box = "alpine/alpine32"
    musl32.vm.box_version = "3.6.0"
    musl32.vm.provision :shell, path: "build-linux-alpine.sh"
  end

  config.vm.define "linux64-musl" do |musl64|
    musl64.vm.box = "generic/alpine36"

    ##  Should use the alpine/alpine64 box, but this issue needs to be fixed first - https://github.com/hashicorp/vagrant/issues/11218 
    # musl64.vm.box = "alpine/alpine64"
    # musl64.vm.box_version = "3.6.0"

    musl64.vm.provision :shell, path: "build-linux-alpine.sh"
  end

  config.vm.provider "virtualbox" do |v|
    v.memory = 2048
    v.cpus = 4
    v.customize ["modifyvm", :id, "--nictype1", "virtio" ]
  end

  if Vagrant.has_plugin?("vagrant-cachier")
    config.cache.scope = :box
  end
  if Vagrant.has_plugin?("vagrant-vbguest")
    config.vbguest.no_install = true
  end

  config.vm.synced_folder "../target", "/rocksdb-build"
  config.vm.synced_folder "../..", "/rocksdb", type: "rsync"
  config.vm.boot_timeout = 1200
end
