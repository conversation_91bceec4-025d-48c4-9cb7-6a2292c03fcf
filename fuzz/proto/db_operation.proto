// Copyright (c) 2011-present, Facebook, Inc.  All rights reserved.
// This source code is licensed under both the GPLv2 (found in the
// COPYING file in the root directory) and Apache 2.0 License
// (found in the LICENSE.Apache file in the root directory).

// Defines database operations.
// Each operation is a key-value pair and an operation type.

syntax = "proto2";

enum OpType {
  PUT = 0;
  MERGE = 1;
  DELETE = 2;
  DELETE_RANGE = 3;
}

message DBOperation {
  required string key = 1;
  // value is ignored for DELETE.
  // [key, value] is the range for DELETE_RANGE.
  optional string value = 2;
  required OpType type = 3;
}

message DBOperations {
  repeated DBOperation operations = 1;
}
