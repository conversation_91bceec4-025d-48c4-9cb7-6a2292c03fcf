# Site settings
permalink: /blog/:year/:month/:day/:title.html
title: RocksDB
tagline: A persistent key-value store for fast storage environments
description: >
  RocksDB is an embeddable persistent key-value store for fast storage.
fbappid: "1615782811974223"
gacode: "UA-49459723-1"
# baseurl determines the subpath of your site. For example if you're using an
# organisation.github.io/reponame/ basic site URL, then baseurl would be set
# as "/reponame" but leave blank if you have a top-level domain URL as it is
# now set to "" by default as discussed in:
# http://jekyllrb.com/news/2016/10/06/jekyll-3-3-is-here/
baseurl: ""

# the base hostname & protocol for your site
# If baseurl is set, then the absolute url for your site would be url/baseurl
# This was also be set to the right thing automatically for local development
# https://github.com/blog/2277-what-s-new-in-github-pages-with-jekyll-3-3
# http://jekyllrb.com/news/2016/10/06/jekyll-3-3-is-here/
url: "http://rocksdb.org"

# Note: There are new filters in Jekyll 3.3 to help with absolute and relative urls
# absolute_url
# relative_url
# So you will see these used throughout the Jekyll code in this template.
# no more need for | prepend: site.url | prepend: site.baseurl
# http://jekyllrb.com/news/2016/10/06/jekyll-3-3-is-here/
#https://github.com/blog/2277-what-s-new-in-github-pages-with-jekyll-3-3

# The GitHub repo for your project
ghrepo: "facebook/rocksdb"

# Use these color settings to determine your colour scheme for the site.
color:
  # primary should be a vivid color that reflects the project's brand
  primary: "#2a2a2a"
  # secondary should be a subtle light or dark color used on page backgrounds
  secondary: "#f9f9f9"
  # Use the following to specify whether the previous two colours are 'light'
  # or 'dark' and therefore what colors can be overlaid on them
  primary-overlay: "dark"
  secondary-overlay: "light"

#Uncomment this if you want to enable Algolia doc search with your own values
#searchconfig:
#  apikey: ""
#  indexname: ""

# Blog posts are builtin to Jekyll by default, with the `_posts` directory.
# Here you can specify other types of documentation. The names here are `docs`
# and `top-level`. This means their content will be in `_docs` and `_top-level`.
# The permalink format is also given.
# http://ben.balter.com/2015/02/20/jekyll-collections/
collections:
  docs:
    output: true
    permalink: /docs/:name/
  top-level:
    output: true
    permalink: :name.html

# DO NOT ADJUST BELOW THIS LINE UNLESS YOU KNOW WHAT YOU ARE CHANGING

markdown: kramdown
kramdown:
  input: GFM
  syntax_highlighter: rouge

  syntax_highlighter_opts:
    css_class: 'rougeHighlight'
    span:
      line_numbers: false
    block:
      line_numbers: true
      start_line: 1

sass:
  style: :compressed

redcarpet:
  extensions: [with_toc_data]

plugins:
  - jekyll-redirect-from
