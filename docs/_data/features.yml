- title: High Performance
  text: |
        RocksDB uses a log structured database engine, written entirely in C++, for maximum performance. Keys and values are just arbitrarily-sized byte streams.
  image: images/promo-performance.svg

- title: Optimized for Fast Storage
  text: |
        RocksDB is optimized for fast, low latency storage such as flash drives and high-speed disk drives. RocksDB exploits the full potential of high read/write rates offered by flash or RAM.
  image: images/promo-flash.svg

- title: Adaptable
  text: |
        RocksDB is adaptable to different workloads. From database storage engines such as [MyRocks](https://github.com/facebook/mysql-5.6) to [application data caching](http://techblog.netflix.com/2016/05/application-data-caching-using-ssds.html) to embedded workloads, RocksDB can be used for a variety of data needs.
  image: images/promo-adapt.svg

- title: Basic and Advanced Database Operations
  text: |
        RocksDB provides basic operations such as opening and closing a database, reading and writing to more advanced operations such as merging and compaction filters.
  image: images/promo-operations.svg
