input[type="search"] {
    -moz-appearance:    none;
    -webkit-appearance: none;
}

.navSearchWrapper {
  align-self: center;
  position: relative;

  &::before {
    border: 3px solid $primary-overlay-special;
    border-radius: 50%;
    content: " ";
    display: block;
    height: 6px;
    left: 15px;
    width: 6px;
    position: absolute;
    top: 4px;
    z-index: 1;
  }

  &::after {
    background: $primary-overlay-special;
    content: " ";
    height: 7px;
    left: 24px;
    position: absolute;
    transform: rotate(-45deg);
    top: 12px;
    width: 3px;
    z-index: 1;
  }

  .aa-dropdown-menu {
    background: $secondary-bg;
    border: 3px solid rgba($text, 0.25);
    color: $text;
    font-size: 14px;
    left: auto !important;
    line-height: 1.2em;
    right: 0 !important;

    .algolia-docsearch-suggestion--category-header {
      background: $primary-overlay-special;
      color: $primary-bg;

      .algolia-docsearch-suggestion--highlight {
        background-color: $primary-bg;
        color: $primary-overlay;
      }
    }

    .algolia-docsearch-suggestion--title .algolia-docsearch-suggestion--highlight,
    .algolia-docsearch-suggestion--subcategory-column .algolia-docsearch-suggestion--highlight {
        color: $primary-bg;
    }

    .algolia-docsearch-suggestion__secondary,
    .algolia-docsearch-suggestion--subcategory-column {
      border-color: rgba($text, 0.3);
    }
  }
}

input#search_input {
  padding-left: 25px;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
  background-color: rgba($primary-overlay-special, 0.25);
  border: none;
  color: rgba($primary-overlay-special, 0);
  outline: none;
  position: relative;
  transition: background-color .2s cubic-bezier(0.68, -0.55, 0.265, 1.55), width .2s cubic-bezier(0.68, -0.55, 0.265, 1.55), color .2s ease;
  width: 60px;

  &:focus, &:active {
    background-color: $secondary-bg;
    color: $text;
    width: 240px;
  }
}

.navigationSlider {
  .navSearchWrapper {
    &::before {
      left: 6px;
      top: 6px;
    }

    &::after {
      left: 15px;
      top: 14px;
    }
  }

  input#search_input_react {
    box-sizing: border-box;
    padding-left: 25px;
    font-size: 14px;
    line-height: 20px;
    border-radius: 20px;
    background-color: rgba($primary-overlay-special, 0.25);
    border: none;
    color: $text;
    outline: none;
    position: relative;
    transition: background-color .2s cubic-bezier(0.68, -0.55, 0.265, 1.55), width .2s cubic-bezier(0.68, -0.55, 0.265, 1.55), color .2s ease;
    width: 100%;

    &:focus, &:active {
      background-color: $primary-bg;
      color: $primary-overlay;
    }
  }

  .algolia-docsearch-suggestion--subcategory-inline {
    display: none;
  }

  & > span {
    width: 100%;
  }

  .aa-dropdown-menu {
    background: $secondary-bg;
    border: 0px solid $secondary-bg;
    color: $text;
    font-size: 12px;
    line-height: 2em;
    max-height: 140px;
    min-width: auto;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
    padding: 0;
    border-radius: 0;
    position: relative !important;
    width: 100%;
  }
}