.button {
  border: 1px solid $primary-bg;
  border-radius: 3px;
  color: $primary-bg;
  display: inline-block;
  font-size: 14px;
  font-weight: 900;
  line-height: 1.2em;
  padding: 10px;
  text-transform: uppercase;
  transition: background 0.3s, color 0.3s;

  &:hover {
    background: $primary-bg;
    color: $primary-overlay;
  }
}

.homeContainer {
  .button {
    border-color: $primary-overlay;
    border-width: 1px;
    color: $primary-overlay;

    &:hover {
      background: $primary-overlay;
      color: $primary-bg;
    }
  }
}

.blockButton {
  display: block;
}

.edit-page-link {
    float: right;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    opacity: 0.6;
    transition: opacity 0.5s;
}

.edit-page-link:hover {
  opacity: 1;
}
