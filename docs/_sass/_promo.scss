.promoSection {
  display: flex;
  flex-flow: column wrap;
  font-size: 125%;
  line-height: 1.6em;
  margin: -10px 0;
  position: relative;
  z-index: 99;

  .promoRow {
    padding: 10px 0;

    .pluginWrapper {
      display: block;

      &.ghWatchWrapper, &.ghStarWrapper {
        height: 28px;
      }
    }

    .pluginRowBlock {
      display: flex;
      flex-flow: row wrap;
      margin: 0 -2px;

      .pluginWrapper {
        padding: 0 2px;
      }
    }
  }
}

iframe.pluginIframe {
  height: 500px;
  margin-top: 20px;
  width: 100%;
}

.iframeContent {
  display: none;
}

.iframePreview {
  display: inline-block;
  margin-top: 20px;
}

@media only screen and (min-width: 1024px) {
  .iframeContent {
    display: block;
  }
  .iframePreview {
    display: none;
  }
}