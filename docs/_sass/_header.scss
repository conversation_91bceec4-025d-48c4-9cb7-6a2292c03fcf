.fixedHeaderContainer {
  background: $primary-bg;
  color: $primary-overlay;
  height: $header-height;
  padding: $header-ptop 0 $header-pbot;
  position: fixed;
  width: 100%;
  z-index: 9999;

  a {
    align-items: center;
    border: 0;
    color: $primary-overlay;
    display: flex;
    flex-flow: row nowrap;
    height: $header-height;
  }

  header {
    display: flex;
    flex-flow: row nowrap;
    position: relative;
    text-align: left;

    img {
      height: 24px;
      margin-right: 10px;
    }

    h2 {
      display: block;
      font-family: $header-font-family;
      font-weight: 900;
      line-height: 18px;
      position: relative;
    }
  }
}

.navigationFull {
  height: 34px;
  margin-left: auto;

  nav {
    position: relative;

    ul {
      display: flex;
      flex-flow: row nowrap;
      margin: 0 -10px;

      li {
        padding: 0 10px;
        display: block;

        a {
          border: 0;
          color: $primary-overlay-special;
          font-size: 16px;
          font-weight: 400;
          line-height: 1.2em;

          &:hover {
            border-bottom: 2px solid $primary-overlay;
            color: $primary-overlay;
          }
        }

        &.navItemActive {
          a {
            color: $primary-overlay;
          }
        }
      }
    }
  }
}

/* 900px


  .fixedHeaderContainer {
    .navigationWrapper {
      nav {
        padding: 0 1em;
        position: relative;
        top: -9px;

        ul {
          margin: 0 -0.4em;
          li {
            display: inline-block;

            a {
              padding: 14px 0.4em;
              border: 0;
              color: $primary-overlay-special;
              display: inline-block;

              &:hover {
                color: $primary-overlay;
              }
            }

            &.navItemActive {
              a {
                color: $primary-overlay;
              }
            }
          }
        }
      }

      &.navigationFull {
        display: inline-block;
      }

      &.navigationSlider {
        display: none;
      }
    }
  }

  1200px

  .fixedHeaderContainer {
    header {
      max-width: 1100px;
    }
  }

  1500px
  .fixedHeaderContainer {
    header {
      max-width: 1400px;
    }
  }
 */