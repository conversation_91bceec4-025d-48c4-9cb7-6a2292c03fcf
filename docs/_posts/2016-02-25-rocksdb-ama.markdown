---
title: RocksDB AMA
layout: post
author: yhchiang
category: blog
redirect_from:
  - /blog/3065/rocksdb-ama/
---

RocksDB developers are doing a Reddit Ask-Me-Anything now at 10AM – 11AM PDT! We welcome you to stop by and ask any RocksDB related questions, including existing / upcoming features, tuning tips, or database design.

Here are some enhancements that we'd like to focus on over the next six months:

* 2-Phase Commit
* Lua support in some custom functions
* Backup and repair tools
* Direct I/O to bypass OS cache
* RocksDB Java API

[https://www.reddit.com/r/IAmA/comments/47k1si/we_are_rocksdb_developers_ask_us_anything/](https://www.reddit.com/r/IAmA/comments/47k1si/we_are_rocksdb_developers_ask_us_anything/)
