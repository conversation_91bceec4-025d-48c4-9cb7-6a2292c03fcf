---
title: RocksDB is now available in Windows Platform
layout: post
author: dmitrism
category: blog
redirect_from:
  - /blog/2033/rocksdb-is-now-available-in-windows-platform/
---

Over the past 6 months we have seen a number of use cases where RocksDB is successfully used by the community and various companies to achieve high throughput and volume in a modern server environment.

We at Microsoft Bing could not be left behind. As a result we are happy to [announce](http://bit.ly/1OmWBT9) the availability of the Windows Port created here at Microsoft which we intend to use as a storage option for one of our key/value data stores.

<!--truncate-->

We are happy to make this available for the community. Keep tuned for more announcements to come.

### Comments

**[Siying <PERSON>](<EMAIL>)**

Appreciate your contributions to RocksDB project! I believe it will benefits many users!

**[empresas sevilla](<EMAIL>)**

Magnifico artículo|, un placer leer el blog

**[jak usunac](<EMAIL>)**

I believe it will benefits too
