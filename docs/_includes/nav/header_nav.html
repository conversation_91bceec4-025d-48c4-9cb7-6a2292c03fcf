<div id="header_nav">
  <div class="navSlideout">
    <i class="menuExpand" id="header_nav_expander"><span></span><span></span><span></span></i>
  </div>
  <nav class="slidingNav">
    <ul>
      {% for item in site.data.nav %}
      <li class="navItem">
        <a href="{{ item.href }}"{% if item.category == "external" %} target="_blank"{% endif %}>{{ item.title }}</a>
      </li>
      {% endfor %}
      {% if site.searchconfig %}
      {% include nav_search.html inputselector="search_input_react" %}
      {% endif %}
    </ul>
  </nav>
</div>
<script>
  var event = document.createEvent('Event');
  event.initEvent('slide', true, true);
  document.addEventListener('slide', function (e) {
    document.body.classList.toggle('sliderActive');
  }, false);
  var headerNav = document.getElementById('header_nav');
  var headerNavExpander = document.getElementById('header_nav_expander');
  headerNavExpander.addEventListener('click', function(e) {
    headerNav.classList.toggle('navSlideoutActive');
    document.dispatchEvent(event);
  }, false);
</script>