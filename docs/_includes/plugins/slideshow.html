<div class="slideshowBlock pluginWrapper" id="slideshow"></div>
<script>
  var slideshowData = [
    {% for image in site.data.slideshow %}
    {
      id         : "{{ image.id }}",
      imagesrc   : "{{ image.src }}",
      tooltip    : "{{ image.tooltip }}",
      href       : "{{ image.link }}",
    },
    {% endfor %}
  ];
</script>
<script src="http://fb.me/react-with-addons-0.13.1.min.js"></script>
<script type="text/javascript">
  var Slideshow = React.createClass({displayName: "Slideshow",
    getInitialState: function() {
      return {
        currentSlide: 0,
      };
    },
    getDefaultProps: function() {
      return {
        data: slideshowData,
      };
    },
    handleSelect: function(id) {
      var index = this.props.data.map(function (el, elIndex) {
        return (
          elIndex
        );
      });
      var currentIndex = index.indexOf(id);
      this.setState({
        currentSlide: currentIndex,
      });
    },
    render: function() {
      return (
        React.createElement("div", {className: "slideshow"},
          React.createElement("div", {className: "slides"},
            this.props.data.map(this.renderSlide)
          ),
          React.createElement("div", {className: "pagination"},
            this.props.data.map(this.renderPager)
          )
        )
      );
    },
    renderSlide: function(child, index) {
      var classes = React.addons.classSet({
        'slide': true,
        'slideActive': this.state.currentSlide === index,
      });
      if (child.href) {
        return (
          React.createElement("div", {key: index, className: classes},
            React.createElement("a", {href: child.href, alt: child.tooltip, title: child.tooltip},
              React.createElement("img", {src: child.imagesrc, alt: child.tooltip, title: child.tooltip})
            )
          )
        );
      }
      return (
        React.createElement("div", {key: index, className: classes},
          React.createElement("img", {src: child.imagesrc, alt: child.tooltip})
        )
      );
    },
    renderPager: function(child, index) {
      var classes = React.addons.classSet({
        'pager': true,
        'pagerActive': this.state.currentSlide === index,
      });
      return (
        React.createElement("span", {key: index, className: classes, onClick: this.handleSelect.bind(this, index)})
      );
    },
  });

  function render(slideshowData) {
    React.render(
      React.createElement(Slideshow, {data: slideshowData}),
      document.getElementById('slideshow')
    );
  }
  render(slideshowData);
</script>