<a
  href="https://twitter.com/share"
  class="twitter-share-button"
  data-url="http://facebook.github.io/fresco{{ page.url }}"
  data-text="Fresco | {{ page.title }}"
  data-hashtags="fresco">Tweet</a>
<div
  class="fb-like"
  data-href="http://facebook.github.io/fresco{{ page.url }}"
  data-layout="standard"
  data-action="like"
  data-show-faces="true"
  data-share="true"></div>

<div id="fb-root"></div>
<script>(function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0];
  if (d.getElementById(id)) return;
  js = d.createElement(s); js.id = id;
  js.src = "//connect.facebook.net/en_US/sdk.js#xfbml=1&version=v2.0";
  fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));</script>

<script>!function(d,s,id){
  var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';
  if(!d.getElementById(id)){
  js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';
  fjs.parentNode.insertBefore(js,fjs);
  }
  }(document, 'script', 'twitter-wjs');
</script>
