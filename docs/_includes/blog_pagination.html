<!-- Pagination links - copied from http://jekyllrb.com/docs/pagination/ -->
{% if paginator.total_pages > 1 %}
<br />
<div class="pagination">
  {% if paginator.previous_page %}
    <a href="{{ paginator.previous_page_path | replace: '//', '/' }}">&laquo; Prev</a>
  {% else %}
    <span>&laquo; Prev</span>
  {% endif %}

  {% for page in (1..paginator.total_pages) %}
    {% if page == paginator.page %}
      <em>{{ page }}</em>
    {% elsif page == 1 %}
      <a href="{{ '/blog' }}">{{ page }}</a>
    {% else %}
      <a href="{{ site.paginate_path | replace: '//', '/' | replace: ':num', page }}">{{ page }}</a>
    {% endif %}
  {% endfor %}

  {% if paginator.next_page %}
    <a href="{{ paginator.next_page_path | replace: '//', '/' }}">Next &raquo;</a>
  {% else %}
    <span>Next &raquo;</span>
  {% endif %}
</div>
<br />
{% endif %}
