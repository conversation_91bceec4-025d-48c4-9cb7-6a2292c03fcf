---
# Only the main Sass file needs front matter (the dashes are enough)
---
@charset "utf-8";

@font-face {
	font-family: 'Lato';
	src: url("{{ '/static/fonts/LatoLatin-Italic.woff2' }}") format('woff2'),
		url("{{ '/static/fonts/LatoLatin-Italic.woff' }}") format('woff');
	font-weight: normal;
	font-style: italic;
}

@font-face {
	font-family: 'Lato';
	src: url("{{ '/static/fonts/LatoLatin-Black.woff2' }}") format('woff2'),
		url("{{ '/static/fonts/LatoLatin-Black.woff' }}") format('woff');
	font-weight: 900;
	font-style: normal;
}

@font-face {
	font-family: 'Lato';
	src: url("{{ '/static/fonts/LatoLatin-BlackItalic.woff2' }}") format('woff2'),
		url("{{ '/static/fonts/LatoLatin-BlackItalic.woff' }}") format('woff');
	font-weight: 900;
	font-style: italic;
}

@font-face {
	font-family: 'Lato';
	src: url("{{ '/static/fonts/LatoLatin-Light.woff2' }}") format('woff2'),
		url("{{ '/static/fonts/LatoLatin-Light.woff' }}") format('woff');
	font-weight: 300;
	font-style: normal;
}

@font-face {
	font-family: 'Lato';
	src: url("{{ '/static/fonts/LatoLatin-Regular.woff2' }}") format('woff2'),
		url("{{ '/static/fonts/LatoLatin-Regular.woff' }}") format('woff');
	font-weight: normal;
	font-style: normal;
}

// Our variables
$base-font-family:  'Lato', Calibri, Arial, sans-serif;
$header-font-family:  'Lato', 'Helvetica Neue', Arial, sans-serif;
$base-font-size:    18px;
$small-font-size:   $base-font-size * 0.875;
$base-line-height:  1.4em;

$spacing-unit:      12px;

// Two configured colors (see _config.yml)
$primary-bg:        				{{ site.color.primary }};
$secondary-bg:      				{{ site.color.secondary }};

// $primary-bg overlays
{% if site.color.primary-overlay == 'light' %}
$primary-overlay:       		darken($primary-bg, 70%);
$primary-overlay-special:		darken($primary-bg, 40%);
{% else %}
$primary-overlay:      		 	#fff;
$primary-overlay-special:		lighten($primary-bg, 30%);
{% endif %}

// $secondary-bg overlays
{% if site.color.secondary-overlay == 'light' %}
$text:              #393939;
$sidenav: 				  darken($secondary-bg, 20%);
$sidenav-text: 			$text;
$sidenav-overlay: 	darken($sidenav, 10%);
$sidenav-active: 		lighten($sidenav, 10%);
{% else %}
$text:              #fff;
$sidenav: 				  lighten($secondary-bg, 20%);
$sidenav-text: 			$text;
$sidenav-overlay: 	lighten($sidenav, 10%);
$sidenav-active: 		darken($sidenav, 10%);
{% endif %}

$code-bg: 					#002b36;

$header-height: 34px;
$header-ptop: 10px;
$header-pbot: 8px;

// Width of the content area
$content-width:     900px;

// Table setting variables
$lightergrey: #F8F8F8;
$greyish: #E8E8E8;
$lightgrey: #B0B0B0;
$green: #2db04b;

// Using media queries with like this:
// @include media-query($on-palm) {
//     .wrapper {
//         padding-right: $spacing-unit / 2;
//         padding-left: $spacing-unit / 2;
//     }
// }
@mixin media-query($device) {
    @media screen and (max-width: $device) {
        @content;
    }
}



// Import partials from `sass_dir` (defaults to `_sass`)
@import
        "reset",
        "base",
				"header",
        "search",
        "syntax-highlighting",
				"promo",
				"buttons",
				"gridBlock",
				"poweredby",
				"footer",
				"react_header_nav",
				"react_docs_nav",
				"tables",
				"blog"
;

// Anchor links
// http://ben.balter.com/2014/03/13/pages-anchor-links/
.header-link {
  position: absolute;
  margin-left: 0.2em;
  opacity: 0;

  -webkit-transition: opacity 0.2s ease-in-out 0.1s;
  -moz-transition: opacity 0.2s ease-in-out 0.1s;
  -ms-transition: opacity 0.2s ease-in-out 0.1s;
}

h2:hover .header-link,
h3:hover .header-link,
h4:hover .header-link,
h5:hover .header-link,
h6:hover .header-link {
  opacity: 1;
}
