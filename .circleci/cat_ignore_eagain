#! /bin/bash

# Work around issue with parallel make output causing random error, as in
# make[1]: write error: stdout
# Probably due to a kernel bug:
#   https://bugs.launchpad.net/ubuntu/+source/linux-signed/+bug/1814393
# Seems to affect image ubuntu-1604:201903-01 and ubuntu-1604:202004-01

cd "$(dirname $0)"

if [ ! -x cat_ignore_eagain.out ]; then
    cc -x c -o cat_ignore_eagain.out - << EOF
#include <unistd.h>
#include <errno.h>
#include <stdio.h>
int main() {
    int n, m, p;
    char buf[1024];
    for (;;) {
        n = read(STDIN_FILENO, buf, 1024);
        if (n > 0 && n <= 1024) {
            for (m = 0; m < n;) {
                p = write(STDOUT_FILENO, buf + m, n - m);
                if (p < 0) {
                    if (errno == EAGAIN) {
                        // ignore but pause a bit
                        usleep(100);
                    } else {
                        perror("write failed");
                        return 42;
                    }
                } else {
                    m += p;
                }
            }
        } else if (n < 0) {
            if (errno == EAGAIN) {
                // ignore but pause a bit
                usleep(100);
            } else {
                // Some non-ignorable error
                perror("read failed");
                return 43;
            }
        } else {
            // EOF
            return 0;
        }
    }
}
EOF
fi

exec ./cat_ignore_eagain.out
