#!/usr/bin/env bash
#
# Detects OS we're compiling on and outputs a file specified by the first
# argument, which in turn gets read while processing <PERSON><PERSON>le.
#
# The output will set the following variables:
#   CC                          C Compiler path
#   CXX                         C++ Compiler path
#   PLATFORM_LDFLAGS            Linker flags
#   J<PERSON><PERSON>_LDFLAGS                Linker flags for RocksDBJava
#   JAVA_STATIC_LDFLAGS         Linker flags for RocksDBJava static build
#   JAVAC_ARGS                  Arguments for javac
#   PLATFORM_SHARED_EXT         Extension for shared libraries
#   PLATFORM_SHARED_LDFLAGS     Flags for building shared library
#   PLATFORM_SHARED_CFLAGS      Flags for compiling objects for shared library
#   PLATFORM_CCFLAGS            C compiler flags
#   PLATFORM_CXXFLAGS           C++ compiler flags.  Will contain:
#   PLATFORM_SHARED_VERSIONED   Set to 'true' if platform supports versioned
#                               shared libraries, empty otherwise.
#   FIND			Command for the find utility
#   WATCH			Command for the watch utility
#
# The PLATFORM_CCFLAGS and PLATFORM_CXXFLAGS might include the following:
#
#       -DROCKSDB_PLATFORM_POSIX    if posix-platform based
#       -<PERSON><PERSON><PERSON>Y                    if the Snappy library is present
#       -DLZ4                       if the LZ4 library is present
#       -DZSTD                      if the ZSTD library is present
#       -DNUMA                      if the NUMA library is present
#       -DTBB                       if the TBB library is present
#       -DMEMKIND                   if the memkind library is present
#
# Using gflags in rocksdb:
# Our project depends on gflags, which requires users to take some extra steps
# before they can compile the whole repository:
#   1. Install gflags. You may download it from here:
#      https://gflags.github.io/gflags/ (Mac users can `brew install gflags`)
#   2. Once installed, add the include path for gflags to your CPATH env var and
#      the lib path to LIBRARY_PATH. If installed with default settings, the lib
#      will be /usr/local/lib and the include path will be /usr/local/include

OUTPUT=$1
if test -z "$OUTPUT"; then
  echo "usage: $0 <output-filename>" >&2
  exit 1
fi

# we depend on C++11, but should be compatible with newer standards
if [ "$ROCKSDB_CXX_STANDARD" ]; then
  PLATFORM_CXXFLAGS="-std=$ROCKSDB_CXX_STANDARD"
else
  PLATFORM_CXXFLAGS="-std=c++11"
fi

# we currently depend on POSIX platform
COMMON_FLAGS="-DROCKSDB_PLATFORM_POSIX -DROCKSDB_LIB_IO_POSIX"

# Default to fbcode gcc on internal fb machines
if [ -z "$ROCKSDB_NO_FBCODE" -a -d /mnt/gvfs/third-party ]; then
    FBCODE_BUILD="true"
    # If we're compiling with TSAN we need pic build
    PIC_BUILD=$COMPILE_WITH_TSAN
    if [ -n "$ROCKSDB_FBCODE_BUILD_WITH_481" ]; then
      # we need this to build with MySQL. Don't use for other purposes.
      source "$PWD/build_tools/fbcode_config4.8.1.sh"
    elif [ -n "$ROCKSDB_FBCODE_BUILD_WITH_5xx" ]; then
      source "$PWD/build_tools/fbcode_config.sh"
    elif [ -n "$ROCKSDB_FBCODE_BUILD_WITH_PLATFORM007" ]; then
      source "$PWD/build_tools/fbcode_config_platform007.sh"
    elif [ -n "$ROCKSDB_FBCODE_BUILD_WITH_PLATFORM009" ]; then
      source "$PWD/build_tools/fbcode_config_platform009.sh"
    elif [ -z "$USE_CLANG" ]; then
        # Still use platform007 for gcc by default for build break on
        # some hosts.
      source "$PWD/build_tools/fbcode_config_platform007.sh"
    else
      source "$PWD/build_tools/fbcode_config_platform009.sh"
    fi
fi

# Delete existing output, if it exists
rm -f "$OUTPUT"
touch "$OUTPUT"

if test -z "$CC"; then
    if [ -x "$(command -v cc)" ]; then
        CC=cc
    elif [ -x "$(command -v clang)" ]; then
        CC=clang
    else
        CC=cc
    fi
fi

if test -z "$CXX"; then
    if [ -x "$(command -v g++)" ]; then
        CXX=g++
    elif [ -x "$(command -v clang++)" ]; then
        CXX=clang++
    else
        CXX=g++
    fi
fi

if test -z "$AR"; then
    if [ -x "$(command -v gcc-ar)" ]; then
        AR=gcc-ar
    elif [ -x "$(command -v llvm-ar)" ]; then
        AR=llvm-ar
    else
        AR=ar
    fi
fi

# Detect OS
if test -z "$TARGET_OS"; then
    TARGET_OS=`uname -s`
fi

if test -z "$TARGET_ARCHITECTURE"; then
    TARGET_ARCHITECTURE=`uname -m`
fi

if test -z "$CLANG_SCAN_BUILD"; then
    CLANG_SCAN_BUILD=scan-build
fi

if test -z "$CLANG_ANALYZER"; then
    CLANG_ANALYZER=$(command -v clang++ 2> /dev/null)
fi

if test -z "$FIND"; then
    FIND=find
fi

if test -z "$WATCH"; then
    WATCH=watch
fi

COMMON_FLAGS="$COMMON_FLAGS ${CFLAGS}"
CROSS_COMPILE=
PLATFORM_CCFLAGS=
PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS"
PLATFORM_SHARED_EXT="so"
PLATFORM_SHARED_LDFLAGS="-Wl,--no-as-needed -shared -Wl,-soname -Wl,"
PLATFORM_SHARED_CFLAGS="-fPIC"
PLATFORM_SHARED_VERSIONED=true

# generic port files (working on all platform by #ifdef) go directly in /port
GENERIC_PORT_FILES=`cd "$ROCKSDB_ROOT"; find port -name '*.cc' | tr "\n" " "`

# On GCC, we pick libc's memcmp over GCC's memcmp via -fno-builtin-memcmp
case "$TARGET_OS" in
    Darwin)
        PLATFORM=OS_MACOSX
        COMMON_FLAGS="$COMMON_FLAGS -DOS_MACOSX"
        PLATFORM_SHARED_EXT=dylib
        PLATFORM_SHARED_LDFLAGS="-dynamiclib -install_name "
        # PORT_FILES=port/darwin/darwin_specific.cc
        ;;
    IOS)
        PLATFORM=IOS
        COMMON_FLAGS="$COMMON_FLAGS -DOS_MACOSX -DIOS_CROSS_COMPILE -DROCKSDB_LITE"
        PLATFORM_SHARED_EXT=dylib
        PLATFORM_SHARED_LDFLAGS="-dynamiclib -install_name "
        CROSS_COMPILE=true
        PLATFORM_SHARED_VERSIONED=
        ;;
    Linux)
        PLATFORM=OS_LINUX
        COMMON_FLAGS="$COMMON_FLAGS -DOS_LINUX"
        if [ -z "$USE_CLANG" ]; then
            COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp"
        else
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -latomic"
        fi
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lpthread -lrt -ldl"
        if test $ROCKSDB_USE_IO_URING; then
            # check for liburing
            $CXX $PLATFORM_CXXFLAGS -x c++ - -luring -o /dev/null 2>/dev/null  <<EOF
              #include <liburing.h>
              int main() {
                struct io_uring ring;
                io_uring_queue_init(1, &ring, 0);
                return 0;
              }
EOF
            if [ "$?" = 0 ]; then
                PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -luring"
                COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_IOURING_PRESENT"
            fi
        fi
        if test -z "$USE_FOLLY_DISTRIBUTED_MUTEX"; then
          USE_FOLLY_DISTRIBUTED_MUTEX=1
        fi
        # PORT_FILES=port/linux/linux_specific.cc
        ;;
    SunOS)
        PLATFORM=OS_SOLARIS
        COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp -D_REENTRANT -DOS_SOLARIS -m64"
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lpthread -lrt -static-libstdc++ -static-libgcc -m64"
        # PORT_FILES=port/sunos/sunos_specific.cc
        ;;
    AIX)
        PLATFORM=OS_AIX
        CC=gcc
        COMMON_FLAGS="$COMMON_FLAGS -maix64 -pthread -fno-builtin-memcmp -D_REENTRANT -DOS_AIX -D__STDC_FORMAT_MACROS"
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -pthread -lpthread -lrt -maix64 -static-libstdc++ -static-libgcc"
        # PORT_FILES=port/aix/aix_specific.cc
        ;;
    FreeBSD)
        PLATFORM=OS_FREEBSD
        CXX=clang++
        COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp -D_REENTRANT -DOS_FREEBSD"
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lpthread"
        # PORT_FILES=port/freebsd/freebsd_specific.cc
        ;;
    GNU/kFreeBSD)
        PLATFORM=OS_GNU_KFREEBSD
        COMMON_FLAGS="$COMMON_FLAGS -DOS_GNU_KFREEBSD"
        if [ -z "$USE_CLANG" ]; then
            COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp"
        else
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -latomic"
        fi
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lpthread -lrt"
        # PORT_FILES=port/gnu_kfreebsd/gnu_kfreebsd_specific.cc
        ;;
    NetBSD)
        PLATFORM=OS_NETBSD
        COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp -D_REENTRANT -DOS_NETBSD"
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lpthread -lgcc_s"
        # PORT_FILES=port/netbsd/netbsd_specific.cc
        ;;
    OpenBSD)
        PLATFORM=OS_OPENBSD
	CXX=clang++
        COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp -D_REENTRANT -DOS_OPENBSD"
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -pthread"
        # PORT_FILES=port/openbsd/openbsd_specific.cc
	FIND=gfind
	WATCH=gnuwatch
        ;;
    DragonFly)
        PLATFORM=OS_DRAGONFLYBSD
        COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp -D_REENTRANT -DOS_DRAGONFLYBSD"
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lpthread"
        # PORT_FILES=port/dragonfly/dragonfly_specific.cc
        ;;
    Cygwin)
        PLATFORM=CYGWIN
        PLATFORM_SHARED_CFLAGS=""
        PLATFORM_CXXFLAGS="-std=gnu++11"
        COMMON_FLAGS="$COMMON_FLAGS -DCYGWIN"
        if [ -z "$USE_CLANG" ]; then
            COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp"
        else
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -latomic"
        fi
        PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lpthread -lrt"
        # PORT_FILES=port/linux/linux_specific.cc
        ;;
    OS_ANDROID_CROSSCOMPILE)
        PLATFORM=OS_ANDROID
	COMMON_FLAGS="$COMMON_FLAGS -fno-builtin-memcmp -D_REENTRANT -DOS_ANDROID -DROCKSDB_PLATFORM_POSIX"
	PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS "  # All pthread features are in the Android C library
        # PORT_FILES=port/android/android.cc
        CROSS_COMPILE=true
        ;;
    *)
        echo "Unknown platform!" >&2
        exit 1
esac

PLATFORM_CXXFLAGS="$PLATFORM_CXXFLAGS ${CXXFLAGS}"
JAVA_LDFLAGS="$PLATFORM_LDFLAGS"
JAVA_STATIC_LDFLAGS="$PLATFORM_LDFLAGS"
JAVAC_ARGS="-source 7"

if [ "$CROSS_COMPILE" = "true" -o "$FBCODE_BUILD" = "true" ]; then
    # Cross-compiling; do not try any compilation tests.
    # Also don't need any compilation tests if compiling on fbcode
    if [ "$FBCODE_BUILD" = "true" ]; then
      # Enable backtrace on fbcode since the necessary libraries are present
      COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_BACKTRACE"
    fi
    true
else
    if ! test $ROCKSDB_DISABLE_FALLOCATE; then
        # Test whether fallocate is available
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <fcntl.h>
          #include <linux/falloc.h>
          int main() {
      int fd = open("/dev/null", 0);
      fallocate(fd, FALLOC_FL_KEEP_SIZE, 0, 1024);
          }
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_FALLOCATE_PRESENT"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_SNAPPY; then
        # Test whether Snappy library is installed
        # http://code.google.com/p/snappy/
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <snappy.h>
          int main() {}
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DSNAPPY"
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lsnappy"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -lsnappy"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_GFLAGS; then
        # Test whether gflags library is installed
        # http://gflags.github.io/gflags/
        # check if the namespace is gflags
        if $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null << EOF
          #include <gflags/gflags.h>
          using namespace GFLAGS_NAMESPACE;
          int main() {}
EOF
        then
          COMMON_FLAGS="$COMMON_FLAGS -DGFLAGS=1"
          PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lgflags"
        # check if namespace is gflags
        elif $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null << EOF
            #include <gflags/gflags.h>
            using namespace gflags;
            int main() {}
EOF
        then
          COMMON_FLAGS="$COMMON_FLAGS -DGFLAGS=1 -DGFLAGS_NAMESPACE=gflags"
          PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lgflags"
        # check if namespace is google
        elif $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null << EOF
            #include <gflags/gflags.h>
            using namespace google;
            int main() {}
EOF
        then
          COMMON_FLAGS="$COMMON_FLAGS -DGFLAGS=1 -DGFLAGS_NAMESPACE=google"
          PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lgflags"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_ZLIB; then
        # Test whether zlib library is installed
        $CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <zlib.h>
          int main() {}
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DZLIB"
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lz"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -lz"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_BZIP; then
        # Test whether bzip library is installed
        $CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <bzlib.h>
          int main() {}
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DBZIP2"
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lbz2"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -lbz2"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_LZ4; then
        # Test whether lz4 library is installed
        $CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <lz4.h>
          #include <lz4hc.h>
          int main() {}
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DLZ4"
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -llz4"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -llz4"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_ZSTD; then
        # Test whether zstd library is installed
        $CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <zstd.h>
          int main() {}
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DZSTD"
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lzstd"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -lzstd"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_NUMA; then
        # Test whether numa is available
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null -lnuma 2>/dev/null  <<EOF
          #include <numa.h>
          #include <numaif.h>
          int main() {}
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DNUMA"
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lnuma"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -lnuma"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_TBB; then
        # Test whether tbb is available
        $CXX $PLATFORM_CXXFLAGS $LDFLAGS -x c++ - -o /dev/null -ltbb 2>/dev/null  <<EOF
          #include <tbb/tbb.h>
          int main() {}
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DTBB"
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -ltbb"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -ltbb"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_JEMALLOC; then
        # Test whether jemalloc is available
        if echo 'int main() {}' | $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null -ljemalloc \
          2>/dev/null; then
            # This will enable some preprocessor identifiers in the Makefile
            JEMALLOC=1
            # JEMALLOC can be enabled either using the flag (like here) or by
            # providing direct link to the jemalloc library
            WITH_JEMALLOC_FLAG=1
            # check for JEMALLOC installed with HomeBrew
            if [ "$PLATFORM" == "OS_MACOSX" ]; then
                if hash brew 2>/dev/null && brew ls --versions jemalloc > /dev/null; then
                    JEMALLOC_VER=$(brew ls --versions jemalloc | tail -n 1 | cut -f 2 -d ' ')
                    JEMALLOC_INCLUDE="-I/usr/local/Cellar/jemalloc/${JEMALLOC_VER}/include"
                    JEMALLOC_LIB="/usr/local/Cellar/jemalloc/${JEMALLOC_VER}/lib/libjemalloc_pic.a"
                    PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS $JEMALLOC_LIB"
                    JAVA_STATIC_LDFLAGS="$JAVA_STATIC_LDFLAGS $JEMALLOC_LIB"
                fi
            fi
        fi
    fi
    if ! test $JEMALLOC && ! test $ROCKSDB_DISABLE_TCMALLOC; then
        # jemalloc is not available. Let's try tcmalloc
        if echo 'int main() {}' | $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null \
          -ltcmalloc 2>/dev/null; then
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -ltcmalloc"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -ltcmalloc"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_MALLOC_USABLE_SIZE; then
        # Test whether malloc_usable_size is available
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <malloc.h>
          int main() {
            size_t res = malloc_usable_size(0);
            (void)res;
            return 0;
          }
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_MALLOC_USABLE_SIZE"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_MEMKIND; then
        # Test whether memkind library is installed
        $CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS -lmemkind -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <memkind.h>
          int main() {
            memkind_malloc(MEMKIND_DAX_KMEM, 1024);
            return 0;
          }
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DMEMKIND"
            PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lmemkind"
            JAVA_LDFLAGS="$JAVA_LDFLAGS -lmemkind"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_PTHREAD_MUTEX_ADAPTIVE_NP; then
        # Test whether PTHREAD_MUTEX_ADAPTIVE_NP mutex type is available
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <pthread.h>
          int main() {
            int x = PTHREAD_MUTEX_ADAPTIVE_NP;
            (void)x;
            return 0;
          }
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_PTHREAD_ADAPTIVE_MUTEX"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_BACKTRACE; then
        # Test whether backtrace is available
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <execinfo.h>
          int main() {
            void* frames[1];
            backtrace_symbols(frames, backtrace(frames, 1));
            return 0;
          }
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_BACKTRACE"
        else
            # Test whether execinfo library is installed
            $CXX $PLATFORM_CXXFLAGS -lexecinfo -x c++ - -o /dev/null 2>/dev/null  <<EOF
              #include <execinfo.h>
              int main() {
                void* frames[1];
                backtrace_symbols(frames, backtrace(frames, 1));
              }
EOF
            if [ "$?" = 0 ]; then
                COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_BACKTRACE"
                PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -lexecinfo"
                JAVA_LDFLAGS="$JAVA_LDFLAGS -lexecinfo"
            fi
        fi
    fi

    if ! test $ROCKSDB_DISABLE_PG; then
        # Test if -pg is supported
        $CXX $PLATFORM_CXXFLAGS -pg -x c++ - -o /dev/null 2>/dev/null  <<EOF
          int main() {
            return 0;
          }
EOF
        if [ "$?" = 0 ]; then
            PROFILING_FLAGS=-pg
        fi
    fi

    if ! test $ROCKSDB_DISABLE_SYNC_FILE_RANGE; then
        # Test whether sync_file_range is supported for compatibility with an old glibc
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <fcntl.h>
          int main() {
            int fd = open("/dev/null", 0);
            sync_file_range(fd, 0, 1024, SYNC_FILE_RANGE_WRITE);
          }
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_RANGESYNC_PRESENT"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_SCHED_GETCPU; then
        # Test whether sched_getcpu is supported
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <sched.h>
          int main() {
            int cpuid = sched_getcpu();
            (void)cpuid;
          }
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_SCHED_GETCPU_PRESENT"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_AUXV_GETAUXVAL; then
        # Test whether getauxval is supported
        $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null 2>/dev/null  <<EOF
          #include <sys/auxv.h>
          int main() {
            uint64_t auxv = getauxval(AT_HWCAP);
            (void)auxv;
          }
EOF
        if [ "$?" = 0 ]; then
            COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_AUXV_GETAUXVAL_PRESENT"
        fi
    fi

    if ! test $ROCKSDB_DISABLE_ALIGNED_NEW; then
        # Test whether c++17 aligned-new is supported
        $CXX $PLATFORM_CXXFLAGS -faligned-new -x c++ - -o /dev/null 2>/dev/null <<EOF
            struct alignas(1024) t {int a;};
            int main() {}
EOF
        if [ "$?" = 0 ]; then
            PLATFORM_CXXFLAGS="$PLATFORM_CXXFLAGS -faligned-new -DHAVE_ALIGNED_NEW"
        fi
    fi
fi

# TODO(tec): Fix -Wshorten-64-to-32 errors on FreeBSD and enable the warning.
# -Wshorten-64-to-32 breaks compilation on FreeBSD i386
if ! [ "$TARGET_OS" = FreeBSD -a "$TARGET_ARCHITECTURE" = i386 ]; then
  # Test whether -Wshorten-64-to-32 is available
  $CXX $PLATFORM_CXXFLAGS -x c++ - -o /dev/null -Wshorten-64-to-32 2>/dev/null  <<EOF
    int main() {}
EOF
  if [ "$?" = 0 ]; then
    COMMON_FLAGS="$COMMON_FLAGS -Wshorten-64-to-32"
  fi
fi

# shall we use HDFS?

if test "$USE_HDFS"; then
  if test -z "$JAVA_HOME"; then
    echo "JAVA_HOME has to be set for HDFS usage." >&2
    exit 1
  fi
  HDFS_CCFLAGS="$HDFS_CCFLAGS -I$JAVA_HOME/include -I$JAVA_HOME/include/linux -DUSE_HDFS -I$HADOOP_HOME/include"
  HDFS_LDFLAGS="$HDFS_LDFLAGS -lhdfs -L$JAVA_HOME/jre/lib/amd64 -L$HADOOP_HOME/lib/native"
  HDFS_LDFLAGS="$HDFS_LDFLAGS -L$JAVA_HOME/jre/lib/amd64/server -L$GLIBC_RUNTIME_PATH/lib"
  HDFS_LDFLAGS="$HDFS_LDFLAGS -ldl -lverify -ljava -ljvm"
  COMMON_FLAGS="$COMMON_FLAGS $HDFS_CCFLAGS"
  PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS $HDFS_LDFLAGS"
  JAVA_LDFLAGS="$JAVA_LDFLAGS $HDFS_LDFLAGS"
fi

if test "0$PORTABLE" -eq 0; then
  if test -n "`echo $TARGET_ARCHITECTURE | grep ^ppc64`"; then
    # Tune for this POWER processor, treating '+' models as base models
    POWER=`LD_SHOW_AUXV=1 /bin/true | grep AT_PLATFORM | grep -E -o power[0-9]+`
    COMMON_FLAGS="$COMMON_FLAGS -mcpu=$POWER -mtune=$POWER "
  elif test -n "`echo $TARGET_ARCHITECTURE | grep ^s390x`"; then
    COMMON_FLAGS="$COMMON_FLAGS -march=z10 "
  elif test -n "`echo $TARGET_ARCHITECTURE | grep -e^arm -e^aarch64`"; then
    # TODO: Handle this with approprite options.
    COMMON_FLAGS="$COMMON_FLAGS"
  elif test -n "`echo $TARGET_ARCHITECTURE | grep ^aarch64`"; then
    COMMON_FLAGS="$COMMON_FLAGS"
  elif [ "$TARGET_OS" == "IOS" ]; then
    COMMON_FLAGS="$COMMON_FLAGS"
  elif [ "$TARGET_OS" == "AIX" ] || [ "$TARGET_OS" == "SunOS" ]; then
    # TODO: Not sure why we don't use -march=native on these OSes
    if test "$USE_SSE"; then
      TRY_SSE_ETC="1"
    fi
  else
    COMMON_FLAGS="$COMMON_FLAGS -march=native "
  fi
else
  # PORTABLE=1
  if test "$USE_SSE"; then
    TRY_SSE_ETC="1"
  fi

  if [[ "${PLATFORM}" == "OS_MACOSX" ]]; then
    # For portability compile for macOS 10.12 (2016) or newer
    COMMON_FLAGS="$COMMON_FLAGS -mmacosx-version-min=10.12"
    PLATFORM_LDFLAGS="$PLATFORM_LDFLAGS -mmacosx-version-min=10.12"
    PLATFORM_SHARED_LDFLAGS="$PLATFORM_SHARED_LDFLAGS -mmacosx-version-min=10.12"
    PLATFORM_CMAKE_FLAGS="-DCMAKE_OSX_DEPLOYMENT_TARGET=10.12"
    JAVA_STATIC_DEPS_COMMON_FLAGS="-mmacosx-version-min=10.12"
    JAVA_STATIC_DEPS_LDFLAGS="$JAVA_STATIC_DEPS_COMMON_FLAGS"
    JAVA_STATIC_DEPS_CCFLAGS="$JAVA_STATIC_DEPS_COMMON_FLAGS"
    JAVA_STATIC_DEPS_CXXFLAGS="$JAVA_STATIC_DEPS_COMMON_FLAGS"
  fi
fi

if test -n "`echo $TARGET_ARCHITECTURE | grep ^ppc64`"; then
  # check for GNU libc on ppc64
  $CXX -x c++ - -o /dev/null 2>/dev/null <<EOF
    #include <stdio.h>
    #include <stdlib.h>
    #include <gnu/libc-version.h>

    int main(int argc, char *argv[]) {
      printf("GNU libc version: %s\n", gnu_get_libc_version());
      return 0;
    }
EOF
  if [ "$?" != 0 ]; then
      PPC_LIBC_IS_GNU=0
  fi
fi

if test "$TRY_SSE_ETC"; then
  # The USE_SSE flag now means "attempt to compile with widely-available
  # Intel architecture extensions utilized by specific optimizations in the
  # source code." It's a qualifier on PORTABLE=1 that means "mostly portable."
  # It doesn't even really check that your current CPU is compatible.
  #
  # SSE4.2 available since nehalem, ca. 2008-2010
  # Includes POPCNT for BitsSetToOne, BitParity
  TRY_SSE42="-msse4.2"
  # PCLMUL available since westmere, ca. 2010-2011
  TRY_PCLMUL="-mpclmul"
  # AVX2 available since haswell, ca. 2013-2015
  TRY_AVX2="-mavx2"
  # BMI available since haswell, ca. 2013-2015
  # Primarily for TZCNT for CountTrailingZeroBits
  TRY_BMI="-mbmi"
  # LZCNT available since haswell, ca. 2013-2015
  # For FloorLog2
  TRY_LZCNT="-mlzcnt"
fi

$CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS $TRY_SSE42 -x c++ - -o /dev/null 2>/dev/null <<EOF
  #include <cstdint>
  #include <nmmintrin.h>
  int main() {
    volatile uint32_t x = _mm_crc32_u32(0, 0);
    (void)x;
  }
EOF
if [ "$?" = 0 ]; then
  COMMON_FLAGS="$COMMON_FLAGS $TRY_SSE42 -DHAVE_SSE42"
elif test "$USE_SSE"; then
  echo "warning: USE_SSE specified but compiler could not use SSE intrinsics, disabling" >&2
fi

$CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS $TRY_PCLMUL -x c++ - -o /dev/null 2>/dev/null <<EOF
  #include <cstdint>
  #include <wmmintrin.h>
  int main() {
    const auto a = _mm_set_epi64x(0, 0);
    const auto b = _mm_set_epi64x(0, 0);
    const auto c = _mm_clmulepi64_si128(a, b, 0x00);
    auto d = _mm_cvtsi128_si64(c);
    (void)d;
  }
EOF
if [ "$?" = 0 ]; then
  COMMON_FLAGS="$COMMON_FLAGS $TRY_PCLMUL -DHAVE_PCLMUL"
elif test "$USE_SSE"; then
  echo "warning: USE_SSE specified but compiler could not use PCLMUL intrinsics, disabling" >&2
fi

$CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS $TRY_AVX2 -x c++ - -o /dev/null 2>/dev/null <<EOF
  #include <cstdint>
  #include <immintrin.h>
  int main() {
    const auto a = _mm256_setr_epi32(0, 1, 2, 3, 4, 7, 6, 5);
    const auto b = _mm256_permutevar8x32_epi32(a, a);
    (void)b;
  }
EOF
if [ "$?" = 0 ]; then
  COMMON_FLAGS="$COMMON_FLAGS $TRY_AVX2 -DHAVE_AVX2"
elif test "$USE_SSE"; then
  echo "warning: USE_SSE specified but compiler could not use AVX2 intrinsics, disabling" >&2
fi

$CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS $TRY_BMI -x c++ - -o /dev/null 2>/dev/null <<EOF
  #include <cstdint>
  #include <immintrin.h>
  int main(int argc, char *argv[]) {
    (void)argv;
    return (int)_tzcnt_u64((uint64_t)argc);
  }
EOF
if [ "$?" = 0 ]; then
  COMMON_FLAGS="$COMMON_FLAGS $TRY_BMI -DHAVE_BMI"
elif test "$USE_SSE"; then
  echo "warning: USE_SSE specified but compiler could not use BMI intrinsics, disabling" >&2
fi

$CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS $TRY_LZCNT -x c++ - -o /dev/null 2>/dev/null <<EOF
  #include <cstdint>
  #include <immintrin.h>
  int main(int argc, char *argv[]) {
    (void)argv;
    return (int)_lzcnt_u64((uint64_t)argc);
  }
EOF
if [ "$?" = 0 ]; then
  COMMON_FLAGS="$COMMON_FLAGS $TRY_LZCNT -DHAVE_LZCNT"
elif test "$USE_SSE"; then
  echo "warning: USE_SSE specified but compiler could not use LZCNT intrinsics, disabling" >&2
fi

$CXX $PLATFORM_CXXFLAGS $COMMON_FLAGS -x c++ - -o /dev/null 2>/dev/null <<EOF
  #include <cstdint>
  int main() {
    uint64_t a = 0xffffFFFFffffFFFF;
    __uint128_t b = __uint128_t(a) * a;
    a = static_cast<uint64_t>(b >> 64);
    (void)a;
  }
EOF
if [ "$?" = 0 ]; then
  COMMON_FLAGS="$COMMON_FLAGS -DHAVE_UINT128_EXTENSION"
fi

# iOS doesn't support thread-local storage, but this check would erroneously
# succeed because the cross-compiler flags are added by the Makefile, not this
# script.
if [ "$PLATFORM" != IOS ]; then
  $CXX $COMMON_FLAGS -x c++ - -o /dev/null 2>/dev/null <<EOF
  #if defined(_MSC_VER) && !defined(__thread)
  #define __thread __declspec(thread)
  #endif
  int main() {
    static __thread int tls;
    (void)tls;
  }
EOF
  if [ "$?" = 0 ]; then
    COMMON_FLAGS="$COMMON_FLAGS -DROCKSDB_SUPPORT_THREAD_LOCAL"
  fi
fi


if [ "$FBCODE_BUILD" != "true" -a "$PLATFORM" = OS_LINUX ]; then
  $CXX $COMMON_FLAGS $PLATFORM_SHARED_CFLAGS -x c++ -c - -o test_dl.o 2>/dev/null <<EOF
  void dummy_func() {}
EOF
  if [ "$?" = 0 ]; then
    $CXX $COMMON_FLAGS $PLATFORM_SHARED_LDFLAGS test_dl.o -o /dev/null 2>/dev/null
    if [ "$?" = 0 ]; then
      EXEC_LDFLAGS+="-ldl"
      rm -f test_dl.o
    fi
  fi
fi

PLATFORM_CCFLAGS="$PLATFORM_CCFLAGS $COMMON_FLAGS"
PLATFORM_CXXFLAGS="$PLATFORM_CXXFLAGS $COMMON_FLAGS"

VALGRIND_VER="$VALGRIND_VER"

ROCKSDB_MAJOR=`build_tools/version.sh major`
ROCKSDB_MINOR=`build_tools/version.sh minor`
ROCKSDB_PATCH=`build_tools/version.sh patch`

echo "CC=$CC" >> "$OUTPUT"
echo "CXX=$CXX" >> "$OUTPUT"
echo "AR=$AR" >> "$OUTPUT"
echo "PLATFORM=$PLATFORM" >> "$OUTPUT"
echo "PLATFORM_LDFLAGS=$PLATFORM_LDFLAGS" >> "$OUTPUT"
echo "PLATFORM_CMAKE_FLAGS=$PLATFORM_CMAKE_FLAGS" >> "$OUTPUT"
echo "JAVA_LDFLAGS=$JAVA_LDFLAGS" >> "$OUTPUT"
echo "JAVA_STATIC_LDFLAGS=$JAVA_STATIC_LDFLAGS" >> "$OUTPUT"
echo "JAVA_STATIC_DEPS_CCFLAGS=$JAVA_STATIC_DEPS_CCFLAGS" >> "$OUTPUT"
echo "JAVA_STATIC_DEPS_CXXFLAGS=$JAVA_STATIC_DEPS_CXXFLAGS" >> "$OUTPUT"
echo "JAVA_STATIC_DEPS_LDFLAGS=$JAVA_STATIC_DEPS_LDFLAGS" >> "$OUTPUT"
echo "JAVAC_ARGS=$JAVAC_ARGS" >> "$OUTPUT"
echo "VALGRIND_VER=$VALGRIND_VER" >> "$OUTPUT"
echo "PLATFORM_CCFLAGS=$PLATFORM_CCFLAGS" >> "$OUTPUT"
echo "PLATFORM_CXXFLAGS=$PLATFORM_CXXFLAGS" >> "$OUTPUT"
echo "PLATFORM_SHARED_CFLAGS=$PLATFORM_SHARED_CFLAGS" >> "$OUTPUT"
echo "PLATFORM_SHARED_EXT=$PLATFORM_SHARED_EXT" >> "$OUTPUT"
echo "PLATFORM_SHARED_LDFLAGS=$PLATFORM_SHARED_LDFLAGS" >> "$OUTPUT"
echo "PLATFORM_SHARED_VERSIONED=$PLATFORM_SHARED_VERSIONED" >> "$OUTPUT"
echo "EXEC_LDFLAGS=$EXEC_LDFLAGS" >> "$OUTPUT"
echo "JEMALLOC_INCLUDE=$JEMALLOC_INCLUDE" >> "$OUTPUT"
echo "JEMALLOC_LIB=$JEMALLOC_LIB" >> "$OUTPUT"
echo "ROCKSDB_MAJOR=$ROCKSDB_MAJOR" >> "$OUTPUT"
echo "ROCKSDB_MINOR=$ROCKSDB_MINOR" >> "$OUTPUT"
echo "ROCKSDB_PATCH=$ROCKSDB_PATCH" >> "$OUTPUT"
echo "CLANG_SCAN_BUILD=$CLANG_SCAN_BUILD" >> "$OUTPUT"
echo "CLANG_ANALYZER=$CLANG_ANALYZER" >> "$OUTPUT"
echo "PROFILING_FLAGS=$PROFILING_FLAGS" >> "$OUTPUT"
echo "FIND=$FIND" >> "$OUTPUT"
echo "WATCH=$WATCH" >> "$OUTPUT"
# This will enable some related identifiers for the preprocessor
if test -n "$JEMALLOC"; then
  echo "JEMALLOC=1" >> "$OUTPUT"
fi
# Indicates that jemalloc should be enabled using -ljemalloc flag
# The alternative is to porvide a direct link to the library via JEMALLOC_LIB
# and JEMALLOC_INCLUDE
if test -n "$WITH_JEMALLOC_FLAG"; then
  echo "WITH_JEMALLOC_FLAG=$WITH_JEMALLOC_FLAG" >> "$OUTPUT"
fi
echo "LUA_PATH=$LUA_PATH" >> "$OUTPUT"
if test -n "$USE_FOLLY_DISTRIBUTED_MUTEX"; then
  echo "USE_FOLLY_DISTRIBUTED_MUTEX=$USE_FOLLY_DISTRIBUTED_MUTEX" >> "$OUTPUT"
fi
if test -n "$PPC_LIBC_IS_GNU"; then
  echo "PPC_LIBC_IS_GNU=$PPC_LIBC_IS_GNU" >> "$OUTPUT"
fi
